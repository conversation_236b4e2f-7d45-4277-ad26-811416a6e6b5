using System.Collections.Generic;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// 汇聚节点提交数据
/// </summary>
public class ParallelJoinNodeSubmit : NodeSubmit
{
    /// <summary>
    /// 需要等待的前置WorkTask名称列表
    /// </summary>
    public List<string> WaitForWorkTasks { get; set; } = new List<string>();

    /// <summary>
    /// 汇聚条件：All(所有完成) 或 Any(任一完成) 或 Custom(自定义脚本)
    /// </summary>
    public JoinConditionSubmit JoinCondition { get; set; } = JoinConditionSubmit.All;

    /// <summary>
    /// 当JoinCondition为Custom时使用的自定义脚本
    /// </summary>
    public string CustomJoinScript { get; set; }
}

/// <summary>
/// 汇聚条件枚举（用于DTO）
/// </summary>
public enum JoinConditionSubmit
{
    /// <summary>
    /// 所有分支都完成
    /// </summary>
    All,
    
    /// <summary>
    /// 任一分支完成
    /// </summary>
    Any,
    
    /// <summary>
    /// 自定义脚本条件
    /// </summary>
    Custom
}
