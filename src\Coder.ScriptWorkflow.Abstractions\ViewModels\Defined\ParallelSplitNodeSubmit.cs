using System.Collections.Generic;

namespace Coder.ScriptWorkflow.ViewModels.Defined;

/// <summary>
/// 并行分支节点提交数据
/// </summary>
public class ParallelSplitNodeSubmit : NodeSubmit
{
    /// <summary>
    /// 下一个节点名称列表，支持多个并行分支
    /// </summary>
    public List<string> NextNodeNames { get; set; } = new List<string>();

    /// <summary>
    /// 重写NextNodeName属性，对于并行分支节点不使用单个下一节点
    /// </summary>
    public override string NextNodeName
    {
        get => null; // 并行分支节点不使用单个下一节点
        set { } // 忽略设置
    }
}
