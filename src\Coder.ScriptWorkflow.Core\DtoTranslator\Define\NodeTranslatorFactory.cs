﻿using System;
using Coder.ScriptWorkflow.Decisions.BooleanDecisions;
using Coder.ScriptWorkflow.DtoTranslator.Define.Decisions;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.ViewModels.Defined;

namespace Coder.ScriptWorkflow.DtoTranslator.Define;

internal static class NodeTranslatorFactory
{
    internal static ITranslator Get(NodeSubmit submit)
    {
        switch (submit)
        {
            case StartNodeSubmit a:
                return new StartNodeTranslator();
            case EndNodeSubmit b:
                return new EndNodeTranslator();
            case BooleanScriptDecisionSubmit c:
                return new ScriptDecisionTranslator();
            case WorkTaskSubmit d:
                return new WorkTaskTranslator();
            case ConditionDecisionSubmit:
                return new ConditionDecisionTranslator();
            case ParallelSplitNodeSubmit:
                return new ParallelSplitNodeTranslator();
            case ParallelJoinNodeSubmit:
                return new ParallelJoinNodeTranslator();
            default:
                throw new ArgumentOutOfRangeException(nameof(submit), $"{submit.GetType().Name}没有被实现。");
        }
    }


    internal static NodeSubmit ToViewModel(Node node)
    {
        switch (node)
        {
            case EndNode endNode:
                return new EndNodeTranslator().ToViewModel(endNode);

            case StartNode startNode:
                return new StartNodeTranslator().ToViewModel(startNode);
            case WorkTask wt:
                return new WorkTaskTranslator().ToViewModel(wt);
            case BoolScriptDecision sd:
                return new ScriptDecisionTranslator().ToViewModel(sd);
            case ConditionDecision cd:
                return new ConditionDecisionTranslator().ToViewModel(cd);
            case ParallelSplitNode psn:
                return new ParallelSplitNodeTranslator().ToViewModel(psn);
            case ParallelJoinNode pjn:
                return new ParallelJoinNodeTranslator().ToViewModel(pjn);
            default:
                throw new ArgumentOutOfRangeException(nameof(node), $"{node.GetType().Name}没有被实现。");
        }
    }
}