using System;
using System.Collections.Generic;
using System.Linq;

namespace Coder.ScriptWorkflow.Nodes;

/// <summary>
/// 并行分支节点 - 将流程分发到多个并行的WorkTask
/// </summary>
public class ParallelSplitNode : Node
{
    /// <summary>
    /// 下一个节点列表，支持多个并行分支
    /// </summary>
    public List<Node> NextNodes { get; set; } = new List<Node>();

    /// <summary>
    /// 构造函数
    /// </summary>
    public ParallelSplitNode()
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="workProcess">工作流定义</param>
    public ParallelSplitNode(WorkProcess workProcess) : base(workProcess)
    {
    }

    /// <summary>
    /// 并行分支节点通常不是自动执行的，需要手动触发分支
    /// </summary>
    public override bool Auto { get; set; } = false;

    /// <summary>
    /// 获取下一个节点 - 对于并行分支节点，这个方法返回false，
    /// 因为需要特殊处理多个下一个节点
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="nextNode">下一个节点（对于并行分支，这里返回null）</param>
    /// <returns>返回false表示需要特殊处理</returns>
    public override bool TryNextNode(IWorkflowContext workflowContext, out Node nextNode)
    {
        nextNode = null;
        return false; // 让WorkflowManager知道需要特殊处理
    }

    /// <summary>
    /// 获取所有下一个节点
    /// </summary>
    /// <returns>下一个节点列表</returns>
    public IEnumerable<Node> GetNextNodes() => NextNodes;

    /// <summary>
    /// 添加下一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    public void AddNextNode(Node node)
    {
        if (node == null)
            throw new ArgumentNullException(nameof(node));
            
        if (!NextNodes.Contains(node))
        {
            NextNodes.Add(node);
        }
    }

    /// <summary>
    /// 移除下一个节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNextNode(Node node)
    {
        if (node != null)
        {
            NextNodes.Remove(node);
        }
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    public override void Validate()
    {
        base.Validate();
        
        if (!NextNodes.Any())
        {
            throw new WorkflowDefinedException($"并行分支节点 '{Name}' 必须至少有一个下一个节点", Name);
        }

        // 验证所有下一个节点都是WorkTask
        var invalidNodes = NextNodes.Where(n => !(n is WorkTask)).ToList();
        if (invalidNodes.Any())
        {
            var invalidNodeNames = string.Join(", ", invalidNodes.Select(n => n.Name));
            throw new WorkflowDefinedException($"并行分支节点 '{Name}' 的下一个节点必须都是WorkTask，但发现非WorkTask节点: {invalidNodeNames}", Name);
        }
    }
}
