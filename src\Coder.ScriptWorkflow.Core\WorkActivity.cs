﻿using System;
using System.Collections.Generic;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.ViewModels.ProcessInstances;

namespace Coder.ScriptWorkflow;

/// <summary>
///     工作活动,工作流中的具体执行单元
/// </summary>
public class WorkActivity
{
    /// <summary>
    ///     默认构造函数，初始化工作活动
    /// </summary>
    public WorkActivity()
    {
        CreateTime = DateTime.Now;
    }

    /// <summary>
    ///     带参数的构造函数，用于创建工作活动实例
    /// </summary>
    /// <param name="pi">流程实例</param>
    /// <param name="workTask">工作任务</param>
    /// <param name="priority">优先级</param>
    public WorkActivity(ProcessInstance pi, WorkTask workTask, Priority priority) : this()
    {
        // ReSharper disable once VirtualMemberCallInConstructor
        ProcessInstance = pi ?? throw new ArgumentNullException(nameof(pi));
        // ReSharper disable once VirtualMemberCallInConstructor

        WorkTask = workTask ?? throw new ArgumentNullException(nameof(workTask));
        Priority = priority;
    }

    /// <summary>
    ///     工作活动唯一标识符
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     优先级别
    /// </summary>
    public Priority Priority { get; set; }

    /// <summary>
    ///     所属工作任务
    /// </summary>
    public virtual WorkTask WorkTask { get; set; }


    /// <summary>
    ///     分配的执行者，可能是用户、角色，部门，
    /// </summary>
    public virtual IEnumerable<Performer> AssignPerformers { get; set; }

    /// <summary>
    ///     工作活动状态
    /// </summary>
    public WorkActivityStatus Status { get; protected set; }

    /// <summary>
    ///     分配时间，由系统分配用户的时间。如果为空，系统还没有分配，或者用户没有接受。
    /// </summary>
    public DateTime? AssignTime { get; set; }

    /// <summary>
    ///     活动分组，同一个workTask 分发多个Group的时候，都会有多个分组
    /// </summary>
    public string TaskCreatingGroup { get; protected internal set; }

    /// <summary>
    /// 并行令牌组ID，用于标识属于同一并行分支的活动
    /// </summary>
    public string ParallelTokenGroup { get; set; }

    /// <summary>
    /// 父级活动ID，用于追溯并行分支的来源
    /// </summary>
    public int? ParentWorkActivityId { get; set; }

    /// <summary>
    ///     用户的登录名称。
    /// </summary>
    public virtual string DisposeUser { get; protected set; }

    /// <summary>
    ///     执行用户的显示名称
    /// </summary>
    public string DisposeUserName { get; set; }


    /// <summary>
    ///     处理完成时间
    /// </summary>
    public DateTime? DisposeTime { get; protected set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreateTime { get; protected set; }

    /// <summary>
    ///     执行的命令
    /// </summary>
    public string Command { get; protected set; }

    /// <summary>
    ///     处理意见
    /// </summary>
    public string Comment { get; set; }

    /// <summary>
    ///     时间跨度
    /// </summary>
    public DateTime? TimeSpan { get; protected set; }


    /// <summary>
    ///     获取或设置对应工作流实例
    /// </summary>
    public virtual ProcessInstance ProcessInstance { get; internal set; }

    /// <summary>
    ///     取消工作活动
    /// </summary>
    public void Cancel()
    {
        if (Status == WorkActivityStatus.Processing || Status == WorkActivityStatus.Suspend ||
            Status == WorkActivityStatus.UnAssign)
            Status = WorkActivityStatus.CloseByAdmin;
        else
            throw new WorkflowException($"工作活动处于${Status}，因此不能取消");
    }

    /// <summary>
    ///     挂起工作活动
    /// </summary>
    public void Suspend()
    {
        if (Status == WorkActivityStatus.Processing || Status == WorkActivityStatus.Suspend ||
            Status == WorkActivityStatus.UnAssign)
            Status = WorkActivityStatus.Suspend;
        else
            throw new WorkflowException($"工作活动处于{Status}，因此不能挂起");
    }

    /// <summary>
    ///     恢复工作活动
    /// </summary>
    public void Resume()
    {
        Status = AssignTime != null ? WorkActivityStatus.Processing : WorkActivityStatus.UnAssign;
    }

    /// <summary>
    ///     处理完成后，重新处理
    /// </summary>
    public void ProcessingByCompleted()
    {
        if (Status == WorkActivityStatus.Complete)
            Status = WorkActivityStatus.Processing;
    }

    /// <summary>
    ///     处理中改为已完成
    /// </summary>
    public void CompletedByProcessing()
    {
        if (Status == WorkActivityStatus.Processing)
            Status = WorkActivityStatus.Complete;
    }

    /// <summary>
    ///     派发这个活动给用户
    /// </summary>
    /// <param name="user">用户登录名字</param>
    /// <param name="userShowName">用户显示名称</param>
    public void AssignTo(string user, string userShowName = null)
    {
        // 如果同一个用户 对此accept 那么不需要抛出异常。
        if (Status != WorkActivityStatus.UnAssign)
            if (DisposeUser != user)
                throw new WorkflowException($"只有状态为{WorkActivityStatus.UnAssign}才能进行分配工单。");

        DisposeUser = user ?? throw new ArgumentNullException(nameof(user));
        DisposeUserName = userShowName;
        AssignTime = DateTime.Now;
        Status = WorkActivityStatus.Processing;
    }

    /// <summary>
    ///     放弃当前这个工作活动
    /// </summary>
    /// <param name="user">用户登录名</param>
    public void Abandon(string user = null)
    {
        if (user != null && DisposeUser != user)
            throw new WorkflowException("你无权改这个工单的执行人");

        if (Status != WorkActivityStatus.Processing)
            throw new WorkflowException("只有状态为" + WorkActivityStatus.Processing +
                                        "才能放弃工单工单。");
        AssignTime = null;
        DisposeUser = null;
        Status = WorkActivityStatus.UnAssign;
    }


    /// <summary>
    ///     设置Resolve结果集合
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    /// <param name="command">命令</param>
    /// <param name="comment">执行建议，如果为空，选址workTask.SuggestComment,如果还是为空，采用command </param>
    /// <param name="resolveTime">处理事件，如果为空，采用当前事件，否则为指定事件，</param>
    public void Resolve(IWorkflowContext workflowContext, string command, string comment = null,
        DateTime? resolveTime = null)
    {
#if DEBUG
        if (workflowContext == null) throw new ArgumentNullException(nameof(workflowContext));
#endif
        //检查当前状态是否能够Resolve。

        switch (Status)
        {
            case WorkActivityStatus.UnAssign:
                throw new WorkflowException("请先分配处理人在使用Resolve方法");
            case WorkActivityStatus.CloseByAdmin:
                throw new WorkflowException("已经关闭，不能被处理");
            case WorkActivityStatus.Complete:
                throw new WorkflowException("已经结束，不能被处理");
            case WorkActivityStatus.Suspend:
                throw new WorkflowException("已经挂起，不能被处理");
        }


        if (string.IsNullOrEmpty(command))
            throw new ArgumentNullException(nameof(command));

        //检查Command 是否正确。
        var actionScript = WorkTask.GetCommand(command);
        if (actionScript == null) throw new WorkflowDefinedException($"{command}不存在，请重新定义", WorkTask.Name);

        DisposeTime = resolveTime ?? DateTime.Now;
        Status = WorkActivityStatus.Complete;
        Command = command;

        if (string.IsNullOrWhiteSpace(comment)) comment = WorkTask.SuggestionComment;

        if (string.IsNullOrWhiteSpace(comment)) comment = Command;

        Comment = comment;
        actionScript.Invoke(workflowContext, this);
    }

    /// <summary>
    ///     form提交
    /// </summary>
    /// <param name="formSubmit">工作活动表单提交数据</param>
    public void MergeForm(WorkActivityForm formSubmit)
    {
        if (formSubmit == null) throw new ArgumentNullException(nameof(formSubmit));
        var jsoObject = formSubmit.FillSelfByJsonForm();
        Comment = formSubmit.WorkActivityComment;
        ProcessInstance.Subject = formSubmit.Subject;
        ProcessInstance.MergeForm(jsoObject);
    }
}