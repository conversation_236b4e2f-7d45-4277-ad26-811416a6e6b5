﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Security;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.Scripts.GlobalScripts;
using Coder.ScriptWorkflow.Scripts.Plugins;
using Coder.ScriptWorkflow.Scripts.Plugins.Console;
using Coder.ScriptWorkflow.Scripts.Plugins.Logger;
using Coder.ScriptWorkflow.Scripts.Plugins.WorkflowManagers;
using Coder.ScriptWorkflow.Scripts.ViewModels;
using Coder.ScriptWorkflow.ViewModels.Defined;
using NiL.JS.Core;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow;

/// <summary>
/// 工作流上下文类，负责管理工作流执行过程中的状态和数据
/// </summary>
public class WorkflowContext : IWorkflowContext
{
    private static readonly string[] defaultPlugin =
    {
        LoggerPlugin.PluginName,
        WorkflowManagerPlugin.PluginName,
        JsConsolePlugin.PluginName
    };

    /// <summary>
    ///     所有工作流定义的全局脚本。
    /// </summary>
    private readonly GlobalScriptContext _globalScriptContext;

    /// <summary>
    /// 插件注入实例字典
    /// </summary>
    private readonly IDictionary<IPlugin, object> _injectInstance = new Dictionary<IPlugin, object>();

    private readonly object _jsGlobalLockItem = new();
    private readonly IEnumerable<Node> _nodes;
    private readonly IEnumerable<IPlugin> _plugins;


    private Context _jsProcessInstanceContext;

    private StartNode _startNode;
    private List<WorkTask> _workTasks;

    /// <summary>
    ///     构建一个工作流上下文，负责在多个node之间传递变量。
    /// </summary>
    /// <param name="globalScriptContext">所有工作流定义的全局js</param>
    /// <param name="processInstance">工作流实例</param>
    /// <param name="nodes">工作流定义的node</param>
    /// <param name="plugins">插件集合</param>
    /// <param name="tags">已经存在的tags</param>
    /// <param name="workflowLogManager">工作流日志</param>
    /// <param name="debugger">调试器</param>
    /// <param name="currentUser">当前用户</param>
    public WorkflowContext([NotNull] GlobalScriptContext globalScriptContext, [NotNull] ProcessInstance processInstance,
        [NotNull] IEnumerable<Node> nodes, IEnumerable<IPlugin> plugins,
        List<ScriptTagInfo> tags, WorkflowLogManager workflowLogManager,
        DebuggerManager debugger, UserViewModel currentUser)
    {
        _globalScriptContext = globalScriptContext;
        _nodes = nodes ?? throw new ArgumentNullException(nameof(nodes));
        _plugins = plugins;
        CurrentUser = currentUser;

        ProcessInstance = processInstance ?? throw new ArgumentNullException(nameof(processInstance));
        AllWorkActivities = Array.Empty<WorkActivity>();
        Logger = workflowLogManager;
        Debugger = debugger;
        Tags = tags; //引用模式，直接采用最顶层那个context就可以保存所有Tags
    }

    /// <summary>
    /// 构造函数，用于创建带有当前工作活动的工作流上下文
    /// </summary>
    /// <param name="globalScriptContext">全局脚本上下文</param>
    /// <param name="nodes">节点集合</param>
    /// <param name="currentWorkActivity">当前工作活动</param>
    /// <param name="relativeWorkActivities">相关工作活动</param>
    /// <param name="tags">标签集合</param>
    /// <param name="workflowLogManager">工作流日志管理器</param>
    /// <param name="debuggerManager">调试器管理器</param>
    /// <exception cref="ArgumentNullException">参数为空时抛出异常</exception>
    public WorkflowContext([NotNull] GlobalScriptContext globalScriptContext,
        [NotNull] IEnumerable<Node> nodes, WorkActivity currentWorkActivity,
        IEnumerable<WorkActivity> relativeWorkActivities, IEnumerable<IPlugin> plugins,
        List<ScriptTagInfo> tags, WorkflowLogManager workflowLogManager, DebuggerManager debuggerManager, UserViewModel currentUser)
        : this(globalScriptContext, currentWorkActivity.ProcessInstance, nodes, plugins, tags, workflowLogManager,
            debuggerManager, currentUser)
    {
        if (globalScriptContext is null) throw new ArgumentNullException(nameof(globalScriptContext));


        if (nodes is null) throw new ArgumentNullException(nameof(nodes));


        if (tags is null) throw new ArgumentNullException(nameof(tags));

        if (workflowLogManager is null) throw new ArgumentNullException(nameof(workflowLogManager));


        CurrentWorkActivity = currentWorkActivity ?? throw new ArgumentNullException(nameof(currentWorkActivity));
        RelativeWorkActivities =
            relativeWorkActivities ?? throw new ArgumentNullException(nameof(relativeWorkActivities));
        CurrentNode = currentWorkActivity.WorkTask;
        var activities = new List<WorkActivity>(RelativeWorkActivities) { CurrentWorkActivity };
        AllWorkActivities = activities;
        Debugger = debuggerManager;
    }

    /// <summary>
    ///     日志记录
    /// </summary>
    public WorkflowLogManager Logger { get; }

    /// <summary>
    /// 是否为试运行模式
    /// </summary>
    public bool IsDryRun { get; set; }
    /// <summary>
    /// 当前用户。
    /// </summary>
    public UserViewModel CurrentUser { get; }

    /// <summary>
    /// 脚本标签信息列表
    /// </summary>
    public List<ScriptTagInfo> Tags { get; }

    /// <summary>
    /// JavaScript全局上下文
    /// </summary>
    public Context JavascriptGlobalContext
    {
        get
        {
            if (_jsProcessInstanceContext == null)
                lock (_jsGlobalLockItem)
                {
                    if (_jsProcessInstanceContext == null)
                    {
                        InitJsGlobal();
                        if (!string.IsNullOrWhiteSpace(ProcessInstance.WorkProcess.GlobalScript))
                            try
                            {
                                _jsProcessInstanceContext.Eval(ProcessInstance.WorkProcess.GlobalScript);
                            }
                            catch (JSException ex)
                            {
                                ex.ToJSException(this, "工作流程定义-全局函数", ProcessInstance.WorkProcess.GlobalScript);
                                throw;
                            }
                    }
                }

            return _jsProcessInstanceContext;
        }
    }

    /// <summary>
    ///     获取开始节点
    /// </summary>
    public StartNode StartNode
    {
        get
        {
            if (_startNode == null)
                    _startNode = (StartNode)_nodes.First(_ => _.Name == StartNodeSubmit.StartNodeName);
            return _startNode;
        }
    }

    /// <summary>
    /// 流程实例
    /// </summary>
    public ProcessInstance ProcessInstance { get; }

    /// <summary>
    /// 工作任务集合
    /// </summary>
    public IEnumerable<WorkTask> WorkTasks
    {
        get
        {
            if (_workTasks != null) return _workTasks;

            _workTasks = new List<WorkTask>();

            foreach (var node in _nodes)
                if (node is WorkTask workTask)
                    _workTasks.Add(workTask);

            return _workTasks;
        }
    }

    /// <summary>
    /// 当前工作活动
    /// </summary>
    public WorkActivity CurrentWorkActivity { get; private set; }

    /// <summary>
    /// 当前节点
    /// </summary>
    public Node CurrentNode { get; set; }

    /// <summary>
    ///     与CurrentWorkActivity相关的工作活动。
    /// </summary>
    public IEnumerable<WorkActivity> RelativeWorkActivities { get; private set; }

    /// <summary>
    /// 所有工作活动
    /// </summary>
    public IEnumerable<WorkActivity> AllWorkActivities { get; private set; }

    /// <summary>
    /// 下一个工作任务的工作流上下文
    /// 当WorkTask 完成，向前到下一个WorktTask的时候，会创建一个新的WorkflowContext。一般在 WorkTask.Auto=true，workTask自动执行。
    /// Next就是下一个WorkTask的WorkflowContext
    /// </summary>
    public IWorkflowContext Next { get; private set; }

    /// <summary>
    ///     创建子的context
    /// </summary>
    /// <param name="workTask"></param>
    /// <returns></returns>
    public IWorkflowContext BuildChildWorkflowContext(WorkTask workTask)
    {
        Next = new WorkflowContext(_globalScriptContext, ProcessInstance, _nodes, _plugins, Tags, Logger, Debugger, this.CurrentUser)
        {
            DistributionUsers = DistributionUsers
        };
        Next.IsDryRun = IsDryRun;
        Next.CurrentNode = workTask;
        return Next;
    }

    /// <summary>
    /// 设置所有工作活动
    /// </summary>
    /// <param name="activitiesInTokenGroup">令牌组中的活动</param>
    /// <exception cref="ArgumentOutOfRangeException">参数超出范围时抛出异常</exception>
    public void SetAllWorkActivities(IEnumerable<WorkActivity> activitiesInTokenGroup)
    {
        if (!AllWorkActivities.Any())
            AllWorkActivities = activitiesInTokenGroup;
        else
            throw new ArgumentOutOfRangeException(nameof(activitiesInTokenGroup), "每次只能设置一次全部工作活动");
    }

    /// <summary>
    /// 切换当前工作活动
    /// </summary>
    /// <param name="workActivity">工作活动</param>
    /// <exception cref="ArgumentNullException">参数为空时抛出异常</exception>
    public void SwitchCurrentWorkActivity(WorkActivity workActivity)
    {
        CurrentWorkActivity = workActivity ?? throw new ArgumentNullException(nameof(workActivity));

        RelativeWorkActivities = AllWorkActivities.Where(_ => workActivity != _);
    }

    /// <summary>
    /// 构建脚本上下文
    /// </summary>
    /// <returns>脚本上下文</returns>
    public Context BuildScriptContext()
    {
        var context = new Context(JavascriptGlobalContext);
        //var form = ProcessInstance.Form ?? "{}";
        //context.Eval(@$"processInstance.Form={form}");
        return context;
    }

    /// <inheritdoc />
    public ISet<string> DistributionUsers { get; private set; } = new HashSet<string>();

    /// <summary>
    /// 调试器管理器
    /// </summary>
    [MaybeNull]
    public DebuggerManager Debugger { get; }


    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        foreach (var item in _injectInstance) item.Key.Dispose(item.Value);
    }

    /// <summary>
    ///     添加Tag，此时还没有持久化，知道当前处理完成才会持久化
    /// </summary>
    /// <param name="newTag"></param>
    /// <param name="color"></param>
    /// <param name="canDelete"></param>
    public void AddTag(string newTag, string color, bool canDelete)
    {
        if (string.IsNullOrEmpty(newTag))
            return;
        var first = Tags.FirstOrDefault(_ => _.TagName == newTag);
        color ??= newTag.StartsWith("处理人:") ? "blue" : Tag.Random();
        if (first != null)
        {
            first.Color = color;
            if (first.TagStatus == TagChanged.Removed) first.TagStatus = TagChanged.Normal;
            return;
        }

        Tags.Add(new ScriptTagInfo
        {
            TagName = newTag,
            Color = color,
            TagStatus = TagChanged.Added
        });
    }

    /// <summary>
    ///     移除tag
    /// </summary>
    /// <param name="newTag"></param>
    public void RemoveTag(string newTag)
    {
        if (string.IsNullOrEmpty(newTag))
            return;
        var first = Tags.FirstOrDefault(_ => _.TagName == newTag);

        if (first != null)
        {
            if (first.ProcessInstanceTagId != 0)
                first.TagStatus = TagChanged.Removed;
            else
                Tags.Remove(first);
        }
    }

    /// <summary>
    /// 初始化JavaScript全局上下文
    /// </summary>
    /// <exception cref="WorkflowException">工作流异常</exception>
    private void InitJsGlobal()
    {
        if (_jsProcessInstanceContext != null) return;
        _jsProcessInstanceContext = new Context(_globalScriptContext.GlobalContext);
      
        foreach (var plugin in _plugins)
        {
            var obj = plugin.GetObject(this);
            _jsProcessInstanceContext.Add(plugin.Name, obj);
            _injectInstance.Add(plugin, obj);
        }


        ProcessInstance.SetJsContext(this.IsDryRun, _jsProcessInstanceContext);

        _jsProcessInstanceContext.DefineVariable("cfg")
            .Assign(
                _jsProcessInstanceContext.GlobalContext.ProxyValue(
                    new ScriptCfg(ProcessInstance.WorkProcess.Configurations))
            );


        var addTag = new Action<string>(newTag => AddTag(newTag, null, true));
        var addTagColor = new Action<string, string>((newTag, color) => AddTag(newTag, color, true));
        var removeTag = new Action<string>(RemoveTag);


        _jsProcessInstanceContext.DefineVariable("addTagColor").Assign(_jsProcessInstanceContext.GlobalContext.ProxyValue(addTagColor));
        _jsProcessInstanceContext.DefineVariable("addTag").Assign(_jsProcessInstanceContext.GlobalContext.ProxyValue(addTag));
        _jsProcessInstanceContext.DefineVariable("removeTag").Assign(_jsProcessInstanceContext.GlobalContext.ProxyValue(removeTag));
    }
}