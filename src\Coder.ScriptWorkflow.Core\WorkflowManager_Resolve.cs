﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Coder.Member.ViewModels.Users;
using Coder.ScriptWorkflow.Assigners;
using Coder.ScriptWorkflow.Debugger;
using Coder.ScriptWorkflow.Logger;
using Coder.ScriptWorkflow.Nodes;
using Coder.ScriptWorkflow.Scripts;
using Coder.ScriptWorkflow.ViewModels;
using Coder.ScriptWorkflow.ViewModels.WorkProcesses;
using NiL.JS.Extensions;

namespace Coder.ScriptWorkflow;

/// <summary>
/// 工作流管理器 - 解析相关功能
/// </summary>
public partial class WorkflowManager
{
    /// <summary>
    /// 处理抄送目标用户
    /// </summary>
    /// <param name="workflowContext">工作流上下文</param>
    private async Task NotifyCcUserAsync(IWorkflowContext workflowContext)
    {
        if (workflowContext?.DistributionUsers == null || !workflowContext.DistributionUsers.Any())
            return;

        var newNotifyUsers = new List<string>();

        // 处理新的抄送用户
        foreach (var ccUserName in workflowContext.DistributionUsers.Where(u => !string.IsNullOrWhiteSpace(u)))
        {
            workflowContext.AddTag(Tag.MakeCCUserTag(ccUserName), Tag.Random(), false);
            newNotifyUsers.Add(ccUserName);
        }

        // 异步通知抄送用户
        if (newNotifyUsers.Any())
        {
            await _interceptorManager.NotifyDistributionUserAsync(workflowContext, newNotifyUsers);
        }
    }

    /// <summary>
    /// 解析工作流上下文
    /// </summary>
    /// <param name="context">工作流上下文</param>
    /// <param name="submit">提交数据</param>
    /// <param name="processInstanceIsComplete">流程实例是否完成</param>
    /// <returns>返回最后一个处理的上下文</returns>
    private IWorkflowContext Resolve(IWorkflowContext context, WorkflowResolveSubmit submit, out bool processInstanceIsComplete)
    {
        // 处理表单数据
        ProcessFormSubmission(context, submit);
        
        // 保存当前工作活动
        Save(context.CurrentWorkActivity);

        processInstanceIsComplete = false;
        
        // 解析工作活动
        var workTaskIsComplete = ResolveWorkActivity(context, submit);
        if (!workTaskIsComplete)
        {
            return context;
        }

        // 记录完成日志
        LogWorkTaskCompletion(context);
        
        // 移动到下一个节点
        return RunToNextNodeWorkflowContext(context, submit, ref processInstanceIsComplete);
    }

    /// <summary>
    /// 处理表单提交
    /// </summary>
    private void ProcessFormSubmission(IWorkflowContext context, WorkflowResolveSubmit submit)
    {
        var json = submit.FillSelfByJsonForm();
        context.ProcessInstance.Subject = submit.Subject;
        context.ProcessInstance.Priority = submit.Priority;
        context.CurrentWorkActivity.ProcessInstance.MergeForm(json);

        // 处理抄送用户
        if (submit.Distributions?.Any() == true)
        {
            var distributionText = $"表单提交生成抄送数据：{string.Join(',', submit.Distributions)}";
            LogDistributionInfo(context, distributionText);
            
            foreach (var distribution in submit.Distributions)
            {
                context.DistributionUsers.Add(distribution);
            }
        }
    }

    /// <summary>
    /// 记录抄送信息
    /// </summary>
    private void LogDistributionInfo(IWorkflowContext context, string message)
    {
        _debuggerManager?.SendMessage(context, message);
        _workflowLogManager.LogDebug(context.ProcessInstance, WorkflowLogType.System, message);
    }

    /// <summary>
    /// 记录工作任务完成
    /// </summary>
    private void LogWorkTaskCompletion(IWorkflowContext context)
    {
        _debuggerManager?.SendMessage(context, "工作任务完成。");
        _workflowLogManager.LogInfo(context, WorkflowLogType.System, "工作任务完成。");
    }

    /// <summary>
    /// 移动到下一个节点
    /// </summary>
    protected IWorkflowContext RunToNextNodeWorkflowContext(IWorkflowContext context, CustomerNextWorkTaskSetting submit, ref bool processInstanceIsComplete)
    {
        var currentContext = context;
        Node nextNode = GetNextNode(context, submit);

        do
        {
            switch (nextNode)
            {
                case EndNode:
                    return HandleEndNode(currentContext, ref processInstanceIsComplete);

                case ParallelSplitNode parallelSplit:
                    return HandleParallelSplit(currentContext, parallelSplit, submit);

                case ParallelJoinNode parallelJoin:
                    return HandleParallelJoin(currentContext, parallelJoin);

                case WorkTask nextWorkTask:
                    return HandleNextWorkTask(currentContext, nextWorkTask, submit, context);
            }

            // 如果节点不是自动执行，则跳出循环
            if (!nextNode.Auto)
                break;
                
        } while (nextNode.TryNextNode(currentContext, out nextNode));

        return currentContext;
    }

    /// <summary>
    /// 获取下一个节点
    /// </summary>
    private Node GetNextNode(IWorkflowContext context, CustomerNextWorkTaskSetting submit)
    {
        if (!string.IsNullOrWhiteSpace(submit.NextWorkTaskName))
        {
            return context.WorkTasks.First(wt => wt.Name == submit.NextWorkTaskName);
        }
        
        context.CurrentNode.TryNextNode(context, out var nextNode);
        return nextNode;
    }

    /// <summary>
    /// 处理结束节点
    /// </summary>
    private IWorkflowContext HandleEndNode(IWorkflowContext context, ref bool processInstanceIsComplete)
    {
        _debuggerManager?.SendMessage(context.ProcessInstance, "完成工作流");
        _workflowLogManager.LogDebug(context.ProcessInstance, WorkflowLogType.System, "完成工作流");
        OnComplete(context);
        processInstanceIsComplete = true;
        return context;
    }

    /// <summary>
    /// 处理下一个工作任务
    /// </summary>
    private IWorkflowContext HandleNextWorkTask(IWorkflowContext currentContext, WorkTask nextWorkTask, CustomerNextWorkTaskSetting submit, IWorkflowContext originalContext)
    {
        currentContext = currentContext.BuildChildWorkflowContext(nextWorkTask);

        var assignResult = GetAssignResult(currentContext, nextWorkTask, submit.Performers);
        var nextWorkActivities = BuildWorkActivity(currentContext, nextWorkTask, currentContext.ProcessInstance, assignResult);
        
        currentContext.SetAllWorkActivities(nextWorkActivities);
        
        var startScriptMessage = $"{nextWorkTask.Name}执行开始脚本。";
        _workflowLogManager.LogDebug(originalContext, WorkflowLogType.Script, startScriptMessage);
        
        if (!originalContext.IsDryRun)
        {
            nextWorkTask.OnWorkTaskStart(currentContext);
        }
        
        return currentContext;
    }

    /// <summary>
    /// 自动解析工作流
    /// </summary>
    private IWorkflowContext AutoResolve(IWorkflowContext autoWorkTaskContext, out bool processInstanceIsComplete)
    {
        ValidateAutoWorkTaskContext(autoWorkTaskContext);
        
        processInstanceIsComplete = false;
        var currentContext = autoWorkTaskContext;
        
        foreach (var currentWorkActivity in autoWorkTaskContext.AllWorkActivities)
        {
            autoWorkTaskContext.SwitchCurrentWorkActivity(currentWorkActivity);
            var currentTaskNode = currentWorkActivity.WorkTask;
            
            var childContext = Resolve(currentContext, new WorkflowResolveSubmit
            {
                Command = currentTaskNode.Commands.First().Name
            }, out processInstanceIsComplete);

            // 工作流程结束或已切换到另一个工作任务，则跳出
            if (processInstanceIsComplete || childContext != currentContext) 
                return currentContext;
        }

        return currentContext;
    }

    /// <summary>
    /// 验证自动工作任务上下文
    /// </summary>
    private void ValidateAutoWorkTaskContext(IWorkflowContext context)
    {
        if (context.CurrentNode.Auto != true)
            throw new InvalidOperationException("此方法只能用于自动执行的工作任务");
            
        if (context.CurrentWorkActivity != null)
            throw new ArgumentException("此方法不能用于有当前工作活动的上下文");
    }

    /// <summary>
    /// 解析工作活动
    /// </summary>
    private static bool ResolveWorkActivity(IWorkflowContext context, WorkflowResolveSubmit submit)
    {
        var disposeName = context.CurrentWorkActivity.DisposeUserName ?? context.CurrentWorkActivity.DisposeUser;

        try
        {
            ExecuteWorkActivityCommand(context, submit, disposeName);
        }
        catch (JsRuntimeException ex)
        {
            HandleJsRuntimeException(context, submit, disposeName, ex);
            throw;
        }

        var isWorkTaskComplete = context.CurrentWorkActivity.WorkTask.OnWorkActivityComplete(context);
        LogWorkTaskCompletionStatus(context, isWorkTaskComplete);

        if (isWorkTaskComplete)
        {
            CompleteWorkTask(context);
        }

        return isWorkTaskComplete;
    }

    /// <summary>
    /// 执行工作活动命令
    /// </summary>
    private static void ExecuteWorkActivityCommand(IWorkflowContext context, WorkflowResolveSubmit submit, string disposeName)
    {
        var commandMessage = $"{disposeName}尝试执行命令";
        context.Debugger?.SendMessage(context, submit.Command, commandMessage);
        context.Logger.LogDebug(context, WorkflowLogType.System, $"{disposeName}尝试执行命令-{submit.Command}");

        context.CurrentWorkActivity.Resolve(context, submit.Command, submit.Comment, submit.DisposeTime);
        
        var successMessage = $"{disposeName}执行命令成功";
        context.Debugger?.SendMessage(context, submit.Command, successMessage);
        context.Logger.LogInfo(context, WorkflowLogType.System, $"{disposeName}执行命令({submit.Command})成功");
    }

    /// <summary>
    /// 处理JavaScript运行时异常
    /// </summary>
    private static void HandleJsRuntimeException(IWorkflowContext context, WorkflowResolveSubmit submit, string disposeName, JsRuntimeException ex)
    {
        var errorMessage = $"{disposeName}执行命令-{submit.Command}失败";
        context.SendJsExceptionDebuggerInfo(ex, errorMessage);
        context.Logger.LogError(context, WorkflowLogType.Defined, ex);
    }

    /// <summary>
    /// 记录工作任务完成状态
    /// </summary>
    private static void LogWorkTaskCompletionStatus(IWorkflowContext context, bool isWorkTaskComplete)
    {
        var statusMessage = $"工作任务是否全部结束:{(isWorkTaskComplete ? "是" : "否")}";
        context.Debugger?.SendMessage(context, WorkActivityScript.WorkActivityScriptEventName, statusMessage);
    }

    /// <summary>
    /// 完成工作任务
    /// </summary>
    private static void CompleteWorkTask(IWorkflowContext context)
    {
        context.CurrentWorkActivity.WorkTask.OnWorkTaskComplete(context);
        context.Debugger?.SendMessage(context, WorkTaskCompleteScript.EventName, "执行完成。");
        CancelRelative(context.RelativeWorkActivities);
    }

    /// <summary>
    /// 处理自动节点
    /// </summary>
    private IWorkflowContext HandlerAutoNode(IWorkflowContext lastContext, string topStartWorkTask, out bool processInstanceIsComplete)
    {
        processInstanceIsComplete = false;
        
        while (lastContext.CurrentNode.Auto && !processInstanceIsComplete)
        {
            var childContext = AutoResolve(lastContext, out processInstanceIsComplete);

            if (childContext == lastContext && !processInstanceIsComplete)
            {
                var errorMessage = $"{lastContext.CurrentNode.Name}工作任务配置出现问题，这个WorkTask应该自动结束，但是实际无法结束。请检查配置并且再次从{topStartWorkTask}执行";
                throw new WorkflowDefinedException(errorMessage);
            }

            if (processInstanceIsComplete) 
                break;

            lastContext = childContext;
        }

        return lastContext;
    }

    /// <summary>
    /// 通过工作活动ID解析
    /// </summary>
    public ResolveResult Resolve(int workActivityId, WorkflowResolveSubmit submit, UserViewModel currentUserName, bool tryRun = false)
    {
        ValidateResolveParameters(submit, workActivityId);
        
        var workActivity = GetWorkActivityById(workActivityId);
        return Resolve(workActivity, submit, currentUserName, tryRun);
    }

    /// <summary>
    /// 验证解析参数
    /// </summary>
    private static void ValidateResolveParameters(WorkflowResolveSubmit submit, int workActivityId)
    {
        if (submit == null) 
            throw new ArgumentNullException(nameof(submit));
            
        if (submit.Command == null) 
            throw new ArgumentNullException(nameof(submit.Command));
            
        if (workActivityId <= 0)
            throw new ArgumentOutOfRangeException(nameof(workActivityId), "必须大于0");
    }

    /// <summary>
    /// 通过工作活动解析
    /// </summary>
    public ResolveResult Resolve(WorkActivity workActivity, WorkflowResolveSubmit submit, UserViewModel currentUserName, bool tryRun = false)
    {
        ValidateWorkActivityResolveParameters(workActivity, submit);

        using var workflowContext = GetWorkflowContext(workActivity, currentUserName);
        workflowContext.IsDryRun = tryRun;
        
        var result = new ResolveResult
        {
            Success = true,
            Message = "处理完成."
        };
        var processInstanceIsComplete = false;
        try
        {
           
            var lastContext = Resolve(workflowContext, submit, out processInstanceIsComplete);

            // 处理自动节点
            if (lastContext != workflowContext && lastContext.CurrentNode.Auto && !processInstanceIsComplete)
            {
                lastContext = HandlerAutoNode(lastContext, workflowContext.CurrentNode.Name, out processInstanceIsComplete);
            }

            // 设置结果信息
            SetResolveResult(result, lastContext, workActivity, processInstanceIsComplete);
        }
        catch (JsRuntimeException jsRuntimeException)
        {
            return HandleJsRuntimeExceptionInResolve(jsRuntimeException, result);
        }
        catch (WorkflowDefinedException ex)
        {
            HandleWorkflowDefinedException(workflowContext, ex);
            throw;
        }
        catch (Exception ex)
        {
            HandleGenericException(workflowContext, ex);
            throw;
        }
        finally
        {
            _workflowLogManager.Flush();
            workflowContext.Dispose();
        }

        // 非试运行模式下执行后续操作
        if (!tryRun)
        {
            ExecutePostResolveOperations(workflowContext, processInstanceIsComplete);
        }

        return result;
    }

    /// <summary>
    /// 验证工作活动解析参数
    /// </summary>
    private static void ValidateWorkActivityResolveParameters(WorkActivity workActivity, WorkflowResolveSubmit submit)
    {
        if (workActivity == null) 
            throw new ArgumentNullException(nameof(workActivity));
            
        if (submit == null) 
            throw new ArgumentNullException(nameof(submit));
            
        if (submit.Command == null) 
            throw new ArgumentNullException(nameof(submit), "submit.Command不能为空。");
    }

    /// <summary>
    /// 设置解析结果
    /// </summary>
    private void SetResolveResult(ResolveResult result, IWorkflowContext lastContext, WorkActivity workActivity, bool processInstanceIsComplete)
    {
        // 如果是切换到下一个workTask，则添加下一个任务的相关工作信息
        if (lastContext.AllWorkActivities.Any() && lastContext.CurrentWorkActivity?.Id != workActivity.Id)
        {
            result.WorkActivities = lastContext.AllWorkActivities.Select(wa => wa.ToSimpleWorkActivity());
            result.AssignScopeType = lastContext.AllWorkActivities.First().WorkTask.Assigner.AssignScopeType;
        }

        result.IsEnd = processInstanceIsComplete;
    }

    /// <summary>
    /// 处理解析中的JavaScript运行时异常
    /// </summary>
    private ResolveResult HandleJsRuntimeExceptionInResolve(JsRuntimeException jsRuntimeException, ResolveResult result)
    {
        _workflowLogManager.LogError(jsRuntimeException);
        result.FillCodeResult(jsRuntimeException);
        result.Success = false;
        result.Message = $"推进工作失败。原因：\"{jsRuntimeException.EventName}\"执行失败。\r\n详情：{jsRuntimeException.Message}。codeLine:{jsRuntimeException.Coordinates}";
        return result;
    }

    /// <summary>
    /// 处理工作流定义异常
    /// </summary>
    private void HandleWorkflowDefinedException(IWorkflowContext workflowContext, WorkflowDefinedException ex)
    {
        workflowContext.SendMessageDebugInfo(ex.Message, DebuggerType.Error);
        _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex, WorkProcess.ModuleOnStart);
    }

    /// <summary>
    /// 处理通用异常
    /// </summary>
    private void HandleGenericException(IWorkflowContext workflowContext, Exception ex)
    {
        _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex, "UNKNOWN-resolve");
    }

    /// <summary>
    /// 执行解析后的操作
    /// </summary>
    private async void ExecutePostResolveOperations(IWorkflowContext workflowContext, bool processInstanceIsComplete)
    {
        await NotifyCcUserAsync(workflowContext);
        PersistentDatabase(workflowContext);
        OnFireEvents(workflowContext, processInstanceIsComplete);
    }

    /// <summary>
    /// 持久化到数据库
    /// </summary>
    private void PersistentDatabase(IWorkflowContext workflowContext)
    {
        using var transManager = _transactionFactory.BeginTransactions();
        try
        {
            var workActivityCount = CalculateWorkActivityCount(workflowContext);
            
            ProcessInstanceStore.AddOrUpdate(workflowContext.ProcessInstance);
            SaveWorkActivities(workflowContext);
            
            workflowContext.ProcessInstance.WorkActivityCount = workActivityCount;
            _tagManager.MergeTags(workflowContext.ProcessInstance, workflowContext.Tags);

            WorkActivityStore.SaveChanges();
            transManager.Commit();
        }
        catch (Exception ex)
        {
            _workflowLogManager.LogError(workflowContext, WorkflowLogType.System, ex);
            transManager.Rollback();
            throw;
        }
        finally
        {
            _workflowLogManager.Flush();
        }
    }

    /// <summary>
    /// 计算工作活动数量
    /// </summary>
    private int CalculateWorkActivityCount(IWorkflowContext workflowContext)
    {
        var workActivityCount = 0;
        var current = workflowContext;
        
        while (current != null)
        {
            workActivityCount += current.AllWorkActivities.Count();
            current = current.Next;
        }
        
        return workActivityCount;
    }

    /// <summary>
    /// 保存工作活动
    /// </summary>
    private void SaveWorkActivities(IWorkflowContext workflowContext)
    {
        var current = workflowContext;
        while (current != null)
        {
            foreach (var workActivity in current.AllWorkActivities)
            {
                WorkActivityStore.AddOrUpdate(workActivity);
            }
            current = current.Next;
        }
    }

    /// <summary>
    /// 触发事件
    /// </summary>
    private void OnFireEvents(IWorkflowContext context, bool workProcessIsComplete)
    {
        var current = context;
        while (current != null)
        {
            foreach (var workActivity in current.AllWorkActivities)
            {
                _interceptorManager.OnWorkActivityChanged(context, workActivity, _serviceProvider);
            }
            current = current.Next;
        }

        if (workProcessIsComplete)
        {
            OnComplete(context);
        }
    }

    /// <summary>
    /// 取消相关的工作活动
    /// </summary>
    private static void CancelRelative(IEnumerable<WorkActivity> relatedWorkActivities)
    {
        var cancelableStatuses = new[] 
        { 
            WorkActivityStatus.Processing, 
            WorkActivityStatus.UnAssign, 
            WorkActivityStatus.Suspend 
        };

        foreach (var workActivity in relatedWorkActivities.Where(wa => cancelableStatuses.Contains(wa.Status)))
        {
            workActivity.Cancel();
        }
    }

    /// <summary>
    /// 获取分配结果
    /// </summary>
    private AssignResult GetAssignResult(IWorkflowContext workflowContext, WorkTask currentWorkTask, IEnumerable<string> nextUsers)
    {
        if (nextUsers?.Any() != true)
        {
            return GetAutoAssignResult(workflowContext, currentWorkTask);
        }

        return GetManualAssignResult(workflowContext, currentWorkTask, nextUsers);
    }

    /// <summary>
    /// 获取自动分配结果
    /// </summary>
    private AssignResult GetAutoAssignResult(IWorkflowContext workflowContext, WorkTask currentWorkTask)
    {
        var lastWorkActivities = WorkActivityStore.GetLastWorkActivity(workflowContext.ProcessInstance, currentWorkTask.Name);
        var activityCount = lastWorkActivities.Count();
        
        _workflowLogManager.LogDebug(workflowContext.ProcessInstance, WorkflowLogType.System,
            $"采用分配器进行分派用户。获取上一次结束的活动:{activityCount}个");
            
        return currentWorkTask.Assign(workflowContext, lastWorkActivities);
    }

    /// <summary>
    /// 获取手动分配结果
    /// </summary>
    private AssignResult GetManualAssignResult(IWorkflowContext workflowContext, WorkTask currentWorkTask, IEnumerable<string> nextUsers)
    {
        var userNames = nextUsers.ToArray();
        _workflowLogManager.LogDebug(workflowContext.ProcessInstance, WorkflowLogType.System,
            $"由客户端指定处理人:{string.Join(',', userNames)}个");

        var performers = _performerManager.GetByUserNamesAsync(userNames).Result
            .Select(user => new Performer
            {
                Key = user.UserName,
                Name = user.Name,
                Type = PerformerType.User
            })
            .ToArray();

        // 手工从Resolve输入的，因此派发模式必须是全部人，并且是手工派发
        return new AssignResult(AssignScopeType.AllOfThem)
        {
            Performers = performers
        };
    }

    /// <summary>
    /// 完成流程实例
    /// </summary>
    private void OnComplete(IWorkflowContext context)
    {
        context.ProcessInstance.Complete();
        
#if DEBUG
        if (context.ProcessInstance.Status != ProcessInstanceStatus.Completed)
            throw new WorkflowException("DEV:工作流实例没有设置为结束的情况下，采用了OnComplete方法");
#endif

        context.ProcessInstance.WorkProcess.OnComplete?.Invoke(context, WorkProcess.ModuleOnComplete);
    }

    /// <summary>
    /// 创建工作活动
    /// </summary>
    private static WorkActivity CreateWorkActivity(WorkTask workTask, string takingGroupId, ProcessInstance instance, AssignResult assignResult)
    {
        return new WorkActivity(instance, workTask, instance.Priority)
        {
            TaskCreatingGroup = takingGroupId,
            AssignPerformers = assignResult.Performers
        };
    }

    /// <summary>
    /// 构建工作活动集合
    /// </summary>
    protected IEnumerable<WorkActivity> BuildWorkActivity(IWorkflowContext context, WorkTask workTask, ProcessInstance instance, AssignResult assignResult)
    {
        ValidateAssignResult(workTask, assignResult);
        
        var taskGroupId = Guid.NewGuid().ToString("n");
        var result = new List<WorkActivity>();
        
        var users = _performerManager.FindByPerformerAsync(assignResult.Performers).Result.ToArray();
        
        switch (assignResult.ScopeType)
        {
            case AssignScopeType.SomeOfThem:
            case AssignScopeType.AllOfThem:
                if (!users.Any())
                {
                    throw new WorkflowDefinedException($"{workTask.Name}执行者设置出现问题:没有任何用户。因此无法进入下一步。");
                }
                
                foreach (var user in users)
                {
                    var workActivity = CreateWorkActivity(workTask, taskGroupId, instance, assignResult);
                    result.Add(workActivity);
                    workActivity.AssignTo(user.UserName, user.Name);
                    context.AddTag(Tag.MakeDisposeUser(user.UserName), null, false);
                }
                break;
                
            case AssignScopeType.OneOfThem:
                var singleWorkActivity = CreateWorkActivity(workTask, taskGroupId, instance, assignResult);
                result.Add(singleWorkActivity);
                break;
        }

        return result;
    }

    /// <summary>
    /// 验证分配结果
    /// </summary>
    private static void ValidateAssignResult(WorkTask workTask, AssignResult assignResult)
    {
        if (!assignResult.Performers.Any())
        {
            throw new WorkflowDefinedException($"{workTask.Name}没有任何执行者，请检查设置。");
        }
    }

    /// < <summary>
    /// 通过有效名称获取工作流程
    /// </summary>
    public async Task<WorkProcessViewModel> GetByEffectNameAsync(string name)
    {
        if (string.IsNullOrWhiteSpace(name)) 
            throw new ArgumentException("名称不能为空", nameof(name));
            
        var workProcess = WorkProcessStore.GetByEffectName(name);
        if (workProcess == null) 
            throw new NotFoundWorkProcessException(name);

        var workTask = await _workTaskStore.GetFirstWorkTaskAsync(workProcess);

        return new WorkProcessViewModel
        {
            FormDesign = workTask.FormDesign ?? workProcess.FormDesign,
            Name = workProcess.Name
        };
    }

    /// <summary>
    /// 处理并行分支节点
    /// </summary>
    /// <param name="context">当前工作流上下文</param>
    /// <param name="parallelSplit">并行分支节点</param>
    /// <param name="submit">提交设置</param>
    /// <returns>处理后的工作流上下文</returns>
    private IWorkflowContext HandleParallelSplit(IWorkflowContext context, ParallelSplitNode parallelSplit, CustomerNextWorkTaskSetting submit)
    {
        _debuggerManager?.SendMessage(context.ProcessInstance, $"开始处理并行分支节点: {parallelSplit.Name}");
        _workflowLogManager.LogDebug(context, WorkflowLogType.System, $"开始处理并行分支节点: {parallelSplit.Name}");

        var parallelContexts = new List<IWorkflowContext>();
        var parallelTokenGroup = Guid.NewGuid().ToString("n");

        foreach (var nextNode in parallelSplit.GetNextNodes())
        {
            if (nextNode is WorkTask workTask)
            {
                var parallelContext = context.BuildChildWorkflowContext(workTask);
                var assignResult = GetAssignResult(parallelContext, workTask, submit.Performers);
                var workActivities = BuildWorkActivity(parallelContext, workTask, context.ProcessInstance, assignResult);

                // 为并行分支设置特殊的TokenGroup，用于后续汇聚识别
                foreach (var activity in workActivities)
                {
                    activity.ParallelTokenGroup = parallelTokenGroup;
                    if (context.CurrentWorkActivity != null)
                    {
                        activity.ParentWorkActivityId = context.CurrentWorkActivity.Id;
                    }
                }

                parallelContext.SetAllWorkActivities(workActivities);
                parallelContexts.Add(parallelContext);

                // 记录并行分支启动日志
                var startScriptMessage = $"并行分支 {workTask.Name} 执行开始脚本。";
                _workflowLogManager.LogDebug(context, WorkflowLogType.Script, startScriptMessage);

                // 执行WorkTask的开始脚本
                if (!context.IsDryRun)
                {
                    workTask.OnWorkTaskStart(parallelContext);
                }
            }
        }

        // 保存所有并行分支的WorkActivity
        foreach (var parallelContext in parallelContexts)
        {
            foreach (var activity in parallelContext.AllWorkActivities)
            {
                WorkActivityStore.AddOrUpdate(activity);
            }
        }

        WorkActivityStore.SaveChanges();

        _debuggerManager?.SendMessage(context.ProcessInstance, $"并行分支节点 {parallelSplit.Name} 处理完成，创建了 {parallelContexts.Count} 个并行分支");
        _workflowLogManager.LogDebug(context, WorkflowLogType.System, $"并行分支节点 {parallelSplit.Name} 处理完成，创建了 {parallelContexts.Count} 个并行分支");

        // 返回第一个并行上下文，或者原上下文
        return parallelContexts.FirstOrDefault() ?? context;
    }

    /// <summary>
    /// 处理汇聚节点
    /// </summary>
    /// <param name="context">当前工作流上下文</param>
    /// <param name="parallelJoin">汇聚节点</param>
    /// <returns>处理后的工作流上下文</returns>
    private IWorkflowContext HandleParallelJoin(IWorkflowContext context, ParallelJoinNode parallelJoin)
    {
        _debuggerManager?.SendMessage(context.ProcessInstance, $"开始检查汇聚节点: {parallelJoin.Name}");
        _workflowLogManager.LogDebug(context, WorkflowLogType.System, $"开始检查汇聚节点: {parallelJoin.Name}");

        // 检查所有需要等待的WorkTask是否都已完成
        var waitingWorkTasks = parallelJoin.WaitForWorkTasks;
        var completedActivities = GetCompletedActivitiesForJoin(context.ProcessInstance, waitingWorkTasks);

        bool canProceed = parallelJoin.JoinCondition switch
        {
            JoinCondition.All => CheckAllBranchesCompleted(waitingWorkTasks, completedActivities),
            JoinCondition.Any => CheckAnyBranchCompleted(waitingWorkTasks, completedActivities),
            JoinCondition.Custom => EvaluateCustomJoinCondition(context, parallelJoin),
            _ => false
        };

        if (!canProceed)
        {
            // 汇聚条件未满足，流程在此等待
            _debuggerManager?.SendMessage(context.ProcessInstance, $"汇聚节点 {parallelJoin.Name} 条件未满足，等待并行分支完成");
            _workflowLogManager.LogDebug(context, WorkflowLogType.System, $"汇聚节点 {parallelJoin.Name} 条件未满足，等待并行分支完成");
            return context;
        }

        // 汇聚条件满足，继续到下一个节点
        _debuggerManager?.SendMessage(context.ProcessInstance, $"汇聚节点 {parallelJoin.Name} 条件满足，继续流程");
        _workflowLogManager.LogDebug(context, WorkflowLogType.System, $"汇聚节点 {parallelJoin.Name} 条件满足，继续流程");

        if (parallelJoin.TryNextNode(context, out var nextNode))
        {
            // 递归处理下一个节点
            var processInstanceIsComplete = false;
            return RunToNextNodeWorkflowContext(context, CustomerNextWorkTaskSetting.FollowWorkProcessSetting, ref processInstanceIsComplete);
        }

        return context;
    }

    /// <summary>
    /// 获取用于汇聚检查的已完成活动
    /// </summary>
    /// <param name="processInstance">流程实例</param>
    /// <param name="waitingWorkTasks">等待的WorkTask名称列表</param>
    /// <returns>已完成的工作活动列表</returns>
    private IEnumerable<WorkActivity> GetCompletedActivitiesForJoin(ProcessInstance processInstance, List<string> waitingWorkTasks)
    {
        return WorkActivityStore.WorkActivities
            .Where(wa => wa.ProcessInstance.Id == processInstance.Id &&
                        waitingWorkTasks.Contains(wa.WorkTask.Name) &&
                        wa.Status == WorkActivityStatus.Complete)
            .ToList();
    }

    /// <summary>
    /// 检查所有分支是否都已完成
    /// </summary>
    /// <param name="waitingWorkTasks">等待的WorkTask名称列表</param>
    /// <param name="completedActivities">已完成的活动列表</param>
    /// <returns>是否所有分支都已完成</returns>
    private bool CheckAllBranchesCompleted(List<string> waitingWorkTasks, IEnumerable<WorkActivity> completedActivities)
    {
        var completedTaskNames = completedActivities.Select(a => a.WorkTask.Name).Distinct().ToList();
        return waitingWorkTasks.All(taskName => completedTaskNames.Contains(taskName));
    }

    /// <summary>
    /// 检查是否有任一分支已完成
    /// </summary>
    /// <param name="waitingWorkTasks">等待的WorkTask名称列表</param>
    /// <param name="completedActivities">已完成的活动列表</param>
    /// <returns>是否有任一分支已完成</returns>
    private bool CheckAnyBranchCompleted(List<string> waitingWorkTasks, IEnumerable<WorkActivity> completedActivities)
    {
        var completedTaskNames = completedActivities.Select(a => a.WorkTask.Name).Distinct().ToList();
        return waitingWorkTasks.Any(taskName => completedTaskNames.Contains(taskName));
    }

    /// <summary>
    /// 评估自定义汇聚条件
    /// </summary>
    /// <param name="context">工作流上下文</param>
    /// <param name="parallelJoin">汇聚节点</param>
    /// <returns>是否满足自定义条件</returns>
    private bool EvaluateCustomJoinCondition(IWorkflowContext context, ParallelJoinNode parallelJoin)
    {
        if (string.IsNullOrWhiteSpace(parallelJoin.CustomJoinScript))
        {
            _workflowLogManager.LogError(context, WorkflowLogType.Script, "自定义汇聚条件脚本为空");
            return false;
        }

        try
        {
            var scriptContext = context.BuildScriptContext();

            // 为脚本提供汇聚相关的上下文信息
            var waitingWorkTasks = parallelJoin.WaitForWorkTasks;
            var completedActivities = GetCompletedActivitiesForJoin(context.ProcessInstance, waitingWorkTasks);

            scriptContext.DefineVariable("waitingWorkTasks").Assign(scriptContext.GlobalContext.ProxyValue(waitingWorkTasks));
            scriptContext.DefineVariable("completedActivities").Assign(scriptContext.GlobalContext.ProxyValue(completedActivities));

            var result = scriptContext.Eval(parallelJoin.CustomJoinScript);
            return result.As<bool>();
        }
        catch (Exception ex)
        {
            _workflowLogManager.LogError(context, WorkflowLogType.Script, $"执行自定义汇聚条件脚本时发生错误: {ex.Message}");
            context.Debugger?.SendMessage(context, $"自定义汇聚条件脚本执行失败: {ex.Message}");
            return false;
        }
    }
}