﻿using Coder.ScriptWorkflow.Nodes;
using Innofactor.EfCoreJsonValueConverter;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Coder.ScriptWorkflow.Mapping;

/// <summary>
/// </summary>
internal class WorkTaskMapping : IEntityTypeConfiguration<WorkTask>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<WorkTask> builder)
    {
        builder.HasBaseType<Node>();
        builder.Property(_ => _.Name).HasMaxLength(32);
        builder.HasOne(_ => _.Assigner);
        builder.Property(_ => _.SuggestionComment).HasMaxLength(50);


        builder.Property(_ => _.FormDesign).HasColumnType("text");
        builder.HasMany(_ => _.Commands);
        builder.Property(_ => _.CanGiveUp);
        builder.HasOne(_ => _.WorkActivityCompleteScript);
        builder.HasOne(_ => _.WorkTaskCompleteScript);
        builder.HasOne(_ => _.WorkTaskStartScript);
        builder.Property(_ => _.ExtendInfo).HasColumnType("text").HasJsonValueConversion();
        builder.Property(_ => _.NextTaskPerformers);
    }
}

/// <summary>
/// </summary>
internal class ParallelJoinNodeMapping : IEntityTypeConfiguration<ParallelJoinNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ParallelJoinNode> builder)
    {
        builder.HasBaseType<Node>();
        builder.Property(_ => _.Name).HasMaxLength(32);

        builder.Property(_ => _.JoinCondition).HasMaxLength(50);


        builder.Property(_ => _.CustomJoinScript).HasColumnType("text");

        builder.Property(_ => _.WaitForWorkTasks).HasColumnType("text").HasJsonValueConversion();
        builder.Ignore(_ => _.Auto);
    }
}


/// <summary>
/// </summary>
internal class ParallelSplitNodeMapping : IEntityTypeConfiguration<ParallelSplitNode>
{
    /// <summary>
    /// </summary>
    /// <param name="builder"></param>
    public void Configure(EntityTypeBuilder<ParallelSplitNode> builder)
    {
        builder.HasBaseType<Node>();
        builder.Property(_ => _.Name).HasMaxLength(32);

        builder.HasMany(_ => _.NextNodes);

     
        builder.Ignore(_ => _.Auto);
    }
}